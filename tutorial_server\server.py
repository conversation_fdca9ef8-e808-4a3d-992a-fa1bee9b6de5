#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Minimal static file server for the Mahjong tutorial site.
Usage:
  python tutorial_server/server.py [PORT]
Default port: 8000
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

DEFAULT_PORT = 8000


def run(port: int):
    base_dir = Path(__file__).parent
    static_dir = base_dir / "static"
    if not static_dir.exists():
        raise SystemExit(f"Static directory not found: {static_dir}")

    # Python 3.7+: SimpleHTTPRequestHandler supports 'directory'
    handler_cls = lambda *args, **kwargs: http.server.SimpleHTTPRequestHandler(
        *args, directory=str(static_dir), **kwargs
    )

    with socketserver.ThreadingTCPServer(("0.0.0.0", port), handler_cls) as httpd:
        print(f"Mahjong tutorial server running: http://127.0.0.1:{port}")
        print(f"Serving directory: {static_dir}")
        print("Press Ctrl+C to stop.")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nShutting down...")


if __name__ == "__main__":
    port = DEFAULT_PORT
    if len(sys.argv) >= 2:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"Invalid port '{sys.argv[1]}', using default {DEFAULT_PORT}")
    run(port)

