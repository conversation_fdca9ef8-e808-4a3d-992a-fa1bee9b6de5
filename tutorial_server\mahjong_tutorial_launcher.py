#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日本麻将教学网站启动器
用户友好的GUI界面，一键启动教学网站
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import webbrowser
import socket
import time
import sys
import os
from pathlib import Path
import http.server
import socketserver

class MahjongTutorialLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.server = None
        self.server_thread = None
        self.port = 8000
        self.is_running = False
        
        self.setup_ui()
        self.get_local_ip()
        
    def setup_ui(self):
        """设置用户界面"""
        self.root.title("日本麻将教学网站")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # 设置图标（如果存在）
        try:
            if getattr(sys, 'frozen', False):
                # 打包后的路径
                base_path = sys._MEIPASS
            else:
                # 开发环境路径
                base_path = Path(__file__).parent
            
            icon_path = Path(base_path) / "icon.ico"
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
        except:
            pass
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🀄 日本麻将新手教学", 
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        subtitle_label = ttk.Label(main_frame, text="一步一步学会无役不胡与振听规则", 
                                  font=("Microsoft YaHei", 10))
        subtitle_label.pack(pady=(0, 20))
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="服务器状态", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.status_label = ttk.Label(status_frame, text="● 未启动", 
                                     foreground="red", font=("Microsoft YaHei", 10))
        self.status_label.pack()
        
        self.url_label = ttk.Label(status_frame, text="", 
                                  foreground="blue", font=("Microsoft YaHei", 9))
        self.url_label.pack()
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.start_button = ttk.Button(button_frame, text="🚀 启动教学网站", 
                                      command=self.start_server, style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="⏹ 停止服务", 
                                     command=self.stop_server, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.browser_button = ttk.Button(button_frame, text="🌐 打开浏览器", 
                                        command=self.open_browser, state=tk.DISABLED)
        self.browser_button.pack(side=tk.LEFT)
        
        # 信息显示
        info_frame = ttk.LabelFrame(main_frame, text="使用说明", padding="10")
        info_frame.pack(fill=tk.BOTH, expand=True)
        
        info_text = tk.Text(info_frame, height=8, wrap=tk.WORD, font=("Microsoft YaHei", 9))
        info_text.pack(fill=tk.BOTH, expand=True)
        
        info_content = """📖 使用方法：
1. 点击"启动教学网站"按钮
2. 等待服务器启动完成
3. 自动打开浏览器，或手动点击"打开浏览器"
4. 开始学习日本麻将规则

🌐 分享给朋友：
• 确保在同一WiFi网络下
• 朋友访问下方显示的局域网地址
• 如无法访问，请检查防火墙设置

📚 学习内容：
• 基本和牌条件与役的概念
• 详细的役种讲解（立直、断幺九、平和等）
• 振听规则的可视化说明
• 分步练习题和综合测验"""
        
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)
        
        # 底部信息
        footer_label = ttk.Label(main_frame, text="© 2025 日本麻将教学 · 仅供学习交流使用", 
                                font=("Microsoft YaHei", 8), foreground="gray")
        footer_label.pack(side=tk.BOTTOM, pady=(10, 0))
        
    def get_local_ip(self):
        """获取本机IP地址"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            self.local_ip = s.getsockname()[0]
            s.close()
        except:
            try:
                self.local_ip = socket.gethostbyname(socket.gethostname())
            except:
                self.local_ip = "127.0.0.1"
    
    def start_server(self):
        """启动服务器"""
        if self.is_running:
            return
            
        try:
            # 获取静态文件目录
            if getattr(sys, 'frozen', False):
                # 打包后的路径
                static_dir = Path(sys._MEIPASS) / "static"
            else:
                # 开发环境路径
                static_dir = Path(__file__).parent / "static"
            
            if not static_dir.exists():
                messagebox.showerror("错误", f"找不到网站文件目录: {static_dir}")
                return
            
            # 创建服务器
            handler_cls = lambda *args, **kwargs: http.server.SimpleHTTPRequestHandler(
                *args, directory=str(static_dir), **kwargs
            )
            
            self.server = socketserver.ThreadingTCPServer(("0.0.0.0", self.port), handler_cls)
            
            # 在新线程中启动服务器
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            
            self.is_running = True
            
            # 更新UI
            self.status_label.config(text="● 运行中", foreground="green")
            self.url_label.config(text=f"本机: http://127.0.0.1:{self.port}\n"
                                      f"局域网: http://{self.local_ip}:{self.port}")
            
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.browser_button.config(state=tk.NORMAL)
            
            # 延迟打开浏览器
            self.root.after(1000, self.open_browser)
            
            messagebox.showinfo("成功", "教学网站已启动！\n浏览器将自动打开。")
            
        except OSError as e:
            if "Address already in use" in str(e):
                messagebox.showerror("错误", f"端口 {self.port} 已被占用\n请关闭其他程序或重启电脑后重试")
            else:
                messagebox.showerror("错误", f"启动服务器失败: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"启动失败: {e}")
    
    def stop_server(self):
        """停止服务器"""
        if not self.is_running:
            return
            
        try:
            if self.server:
                self.server.shutdown()
                self.server.server_close()
                self.server = None
            
            self.is_running = False
            
            # 更新UI
            self.status_label.config(text="● 已停止", foreground="red")
            self.url_label.config(text="")
            
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.browser_button.config(state=tk.DISABLED)
            
            messagebox.showinfo("提示", "服务器已停止")
            
        except Exception as e:
            messagebox.showerror("错误", f"停止服务器失败: {e}")
    
    def open_browser(self):
        """打开浏览器"""
        if self.is_running:
            try:
                webbrowser.open(f"http://127.0.0.1:{self.port}")
            except Exception as e:
                messagebox.showerror("错误", f"无法打开浏览器: {e}")
        else:
            messagebox.showwarning("提示", "请先启动服务器")
    
    def on_closing(self):
        """关闭程序时的处理"""
        if self.is_running:
            self.stop_server()
        self.root.destroy()
    
    def run(self):
        """运行程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = MahjongTutorialLauncher()
        app.run()
    except Exception as e:
        messagebox.showerror("启动失败", f"程序启动时发生错误:\n{str(e)}")

if __name__ == "__main__":
    main()
