('D:\\alone\\2.0\\MajsoulMax-main\\tutorial_server\\dist\\日本麻将教学网站.exe',
 <PERSON><PERSON><PERSON>,
 <PERSON><PERSON>e,
 False,
 'D:\\alone\\2.0\\MajsoulMax-main\\.venv\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-windowed.ico',
 None,
 <PERSON>alse,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'D:\\alone\\2.0\\MajsoulMax-main\\tutorial_server\\build\\mahjong_tutorial\\日本麻将教学网站.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\alone\\2.0\\MajsoulMax-main\\tutorial_server\\build\\mahjong_tutorial\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\alone\\2.0\\MajsoulMax-main\\tutorial_server\\build\\mahjong_tutorial\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\alone\\2.0\\MajsoulMax-main\\tutorial_server\\build\\mahjong_tutorial\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\alone\\2.0\\MajsoulMax-main\\tutorial_server\\build\\mahjong_tutorial\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\alone\\2.0\\MajsoulMax-main\\tutorial_server\\build\\mahjong_tutorial\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\alone\\2.0\\MajsoulMax-main\\tutorial_server\\build\\mahjong_tutorial\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\alone\\2.0\\MajsoulMax-main\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\alone\\2.0\\MajsoulMax-main\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\alone\\2.0\\MajsoulMax-main\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('mahjong_tutorial_launcher',
   'D:\\alone\\2.0\\MajsoulMax-main\\tutorial_server\\mahjong_tutorial_launcher.py',
   'PYSOURCE'),
  ('python312.dll', 'D:\\dev\\Python312\\python312.dll', 'BINARY'),
  ('_decimal.pyd', 'D:\\dev\\Python312\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\dev\\Python312\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\dev\\Python312\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\dev\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\dev\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\dev\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\dev\\Python312\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\dev\\Python312\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_tkinter.pyd', 'D:\\dev\\Python312\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\dev\\Python312\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'D:\\dev\\Python312\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'D:\\dev\\Python312\\DLLs\\libssl-3.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\dev\\Python312\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'D:\\dev\\Python312\\DLLs\\tcl86t.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('zlib1.dll', 'D:\\dev\\Python312\\DLLs\\zlib1.dll', 'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\auto.tcl', 'D:\\dev\\Python312\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'D:\\dev\\Python312\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tk_data\\menu.tcl', 'D:\\dev\\Python312\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-ru.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\koi8-ru.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tk_data\\entry.tcl', 'D:\\dev\\Python312\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tk_data\\scale.tcl', 'D:\\dev\\Python312\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\dev\\Python312\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tk_data\\focus.tcl', 'D:\\dev\\Python312\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'D:\\dev\\Python312\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:\\dev\\Python312\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\dev\\Python312\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tm.tcl', 'D:\\dev\\Python312\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.8.tm',
   'D:\\dev\\Python312\\tcl\\tcl8\\8.5\\tcltest-2.5.8.tm',
   'DATA'),
  ('_tk_data\\icons.tcl', 'D:\\dev\\Python312\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tk_data\\text.tcl', 'D:\\dev\\Python312\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\safe.tcl', 'D:\\dev\\Python312\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tk_data\\tclIndex', 'D:\\dev\\Python312\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\dev\\Python312\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-t.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\koi8-t.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\word.tcl', 'D:\\dev\\Python312\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tclIndex', 'D:\\dev\\Python312\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tk_data\\tk.tcl', 'D:\\dev\\Python312\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:\\dev\\Python312\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\init.tcl', 'D:\\dev\\Python312\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\dev\\Python312\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('base_library.zip',
   'D:\\alone\\2.0\\MajsoulMax-main\\tutorial_server\\build\\mahjong_tutorial\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1755236878,
 [('runw.exe',
   'D:\\alone\\2.0\\MajsoulMax-main\\.venv\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'D:\\dev\\Python312\\python312.dll')
