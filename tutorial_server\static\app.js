const quizData = [
  {
    id: 1,
    text: "门清听两面（3万/6万），你的舍牌里有一张3万。此时他家打出3万。你能否荣和？",
    options: ["能荣和", "只能自摸", "无法和"],
    answer: 1, // 只能自摸（舍张振听禁止荣和）
    explain: "你处于舍张振听，不能对3万荣和，但仍可自摸3万或荣/自摸6万。"
  },
  {
    id: 2,
    text: "副露后的断幺九（不含1/9/字）是否依然是一番役？",
    options: ["是", "否", "只在门清时算"],
    answer: 0,
    explain: "断幺九无门清限制，副露也可成立。与立直不同。"
  },
  {
    id: 3,
    text: "立直后同巡你放过一张可荣和的牌，这一巡内是否还可以改判去荣和？",
    options: ["可以", "不可以，下一巡解除", "永久不能和"],
    answer: 1,
    explain: "这是同巡振听：当前巡内放过和张则本巡内不可再荣和，下一巡自动解除。"
  },
  {
    id: 4,
    text: "听单骑将牌，是否一定有平和？",
    options: ["是", "否"],
    answer: 1,
    explain: "平和要求四面子皆顺子、将为非役牌、且和牌为两面。单骑不满足两面和条件。"
  },
  {
    id: 5,
    text: "场风为东，你手里碰出东刻子。是否满足役牌？",
    options: ["是", "否", "要门清才算"],
    answer: 0,
    explain: "役牌（场风/门风/三元）无门清限制，碰出刻子即可成立一番。"
  }
];

function renderQuiz() {
  const root = document.getElementById('quiz-root');
  root.innerHTML = '';
  quizData.forEach((q, idx) => {
    const card = document.createElement('div');
    card.className = 'card';
    const h = document.createElement('h3');
    h.textContent = `题目 ${idx + 1}`;
    const p = document.createElement('p');
    p.textContent = q.text;

    const list = document.createElement('ul');
    list.style.listStyle = 'none';
    list.style.paddingLeft = '0';

    q.options.forEach((opt, i) => {
      const li = document.createElement('li');
      const id = `q${q.id}_opt${i}`;
      li.innerHTML = `<label><input type="radio" name="q${q.id}" value="${i}" id="${id}"> ${opt}</label>`;
      list.appendChild(li);
    });

    card.appendChild(h);
    card.appendChild(p);
    card.appendChild(list);
    root.appendChild(card);
  });
}

function checkAnswers() {
  let correct = 0;
  quizData.forEach(q => {
    const sel = document.querySelector(`input[name="q${q.id}"]:checked`);
    if (!sel) return;
    if (Number(sel.value) === q.answer) correct++;
  });
  const result = document.getElementById('quiz-result');
  result.textContent = `共 ${quizData.length} 题，答对 ${correct} 题。`;

  // 展示解析
  const root = document.getElementById('quiz-root');
  const cards = root.querySelectorAll('.card');
  quizData.forEach((q, idx) => {
    let explainEl = cards[idx].querySelector('.explain');
    if (!explainEl) {
      explainEl = document.createElement('p');
      explainEl.className = 'tip explain';
      cards[idx].appendChild(explainEl);
    }
    explainEl.textContent = `解析：${q.explain}`;
  });
}

window.addEventListener('DOMContentLoaded', () => {
  renderQuiz();
  document.getElementById('check-btn').addEventListener('click', checkAnswers);
});

