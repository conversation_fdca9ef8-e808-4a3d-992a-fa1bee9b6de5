# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 获取当前目录 - 在spec文件中使用SPECPATH
current_dir = Path(SPECPATH).parent

# 静态文件目录
static_dir = current_dir / "static"

# 收集所有静态文件
static_files = []
if static_dir.exists():
    for file_path in static_dir.rglob("*"):
        if file_path.is_file():
            # 计算相对路径
            rel_path = file_path.relative_to(current_dir)
            static_files.append((str(file_path), str(rel_path.parent)))

block_cipher = None

a = Analysis(
    ['mahjong_tutorial_launcher.py'],
    pathex=[str(current_dir)],
    binaries=[],
    datas=static_files,  # 包含所有静态文件
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'http.server',
        'socketserver',
        'webbrowser',
        'socket',
        'threading',
        'pathlib',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='日本麻将教学网站',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
    version_info={
        'version': (1, 0, 0, 0),
        'description': '日本麻将新手教学网站',
        'product_name': '日本麻将教学',
        'product_version': '1.0.0',
        'company_name': 'Mahjong Tutorial',
        'copyright': '© 2025 日本麻将教学',
    }
)
